import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi } from 'vitest';
import CreatePledge from '../CreatePledge';

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: { subscription: { unsubscribe: vi.fn() } },
      })),
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
      })),
    })),
  },
}));

// Mock toast
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: vi.fn() }),
}));

// Mock loading spinner
vi.mock('@/components/ui/loading-spinner', () => ({
  default: ({ text }: { text: string }) => <div data-testid="loading-spinner">{text}</div>,
}));

const mockProfile = {
  id: 'test-user-id',
  treasurer_name: 'John Doe',
  bride_name: 'Jane',
  groom_name: 'John',
  wedding_date: '2024-12-25',
  venue: 'Test Venue',
  treasurer_phone: '+256777123456',
  theme: 'sunset',
  special_message: 'Test message',
};

const renderCreatePledge = () => {
  return render(
    <BrowserRouter>
      <CreatePledge />
    </BrowserRouter>
  );
};

describe('CreatePledge Page', () => {
  let supabase: { auth: { getSession: any }; from: any };
  let toast: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    const supabaseModule = await import('@/integrations/supabase/client');
    const toastModule = await import('@/hooks/use-toast');
    supabase = supabaseModule.supabase;
    toast = (toastModule.useToast as () => { toast: any })().toast;

    // Default successful session
    supabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'test-user-id' } } },
    });
  });

  it('shows loading spinner while fetching profile', () => {
    // Mock delayed response
    supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockImplementation(
            () => new Promise(resolve => setTimeout(() => resolve({ data: mockProfile }), 100))
          ),
        }),
      }),
    });

    renderCreatePledge();

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading your profile...')).toBeInTheDocument();
  });

  it('renders create pledge form when profile is loaded', async () => {
    supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
        }),
      }),
    });

    renderCreatePledge();

    await waitFor(() => {
      expect(screen.getByText('Create Pledge Card')).toBeInTheDocument();
      expect(screen.getByText('Customize your wedding pledge card to share with family and friends')).toBeInTheDocument();
    });
  });

  it('shows manage pledge card when card is already created', async () => {
    const profileWithCard = { ...mockProfile, theme: 'royal', special_message: 'Custom message' };
    
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: profileWithCard, error: null }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      expect(screen.getByText('Manage Pledge Card')).toBeInTheDocument();
      expect(screen.getByText('Update and share your wedding pledge card with guests')).toBeInTheDocument();
    });
  });

  it('displays theme selection options', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      expect(screen.getByText('Theme')).toBeInTheDocument();
      expect(screen.getByText('Sunset Romance')).toBeInTheDocument();
    });
  });

  it('allows editing special message', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      const textarea = screen.getByLabelText('Special Message');
      expect(textarea).toBeInTheDocument();
      expect(textarea).toHaveValue('Join us in celebrating our special day with your loving support and contributions.');
    });
    
    const textarea = screen.getByLabelText('Special Message');
    fireEvent.change(textarea, { target: { value: 'New custom message' } });
    
    expect(textarea).toHaveValue('New custom message');
  });

  it('handles theme selection', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      const themeSelect = screen.getByText('Sunset Romance');
      expect(themeSelect).toBeInTheDocument();
    });
  });

  it('creates pledge card successfully', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
        }),
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
          }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      const createButton = screen.getByText('Create Pledge Card');
      fireEvent.click(createButton);
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Success!',
        description: 'Your pledge card has been created successfully!',
      });
    });
  });

  it('handles back to dashboard navigation', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      const backButton = screen.getByText('Back to Dashboard');
      fireEvent.click(backButton);
    });
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  it('handles preview functionality', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      const previewButton = screen.getByText('Preview');
      fireEvent.click(previewButton);
    });
    
    expect(mockNavigate).toHaveBeenCalledWith('/pledge/test-user-id');
  });

  it('redirects to auth when no session', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({ data: { session: null } });
    
    mockSupabase.from.mockReturnValue({
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: null, error: null }),
          }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    // Try to create pledge card without session
    await waitFor(() => {
      if (screen.queryByText('Create Pledge Card')) {
        const createButton = screen.getByText('Create Pledge Card');
        fireEvent.click(createButton);
      }
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Authentication Required',
        description: 'Please sign in to create a pledge card',
        variant: 'destructive',
      });
    });
  });

  it('handles share functionality', async () => {
    const profileWithCard = { ...mockProfile, theme: 'royal', special_message: 'Custom message' };
    
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: profileWithCard, error: null }),
        }),
      }),
    });
    
    // Mock navigator.share
    const mockShare = vi.fn().mockResolvedValue(undefined);
    Object.defineProperty(navigator, 'share', {
      value: mockShare,
      writable: true,
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      const shareButton = screen.getByText('Share');
      fireEvent.click(shareButton);
    });
    
    expect(mockShare).toHaveBeenCalledWith({
      title: 'Jane & John\'s Wedding Pledge Card',
      text: 'Join us in celebrating our special day! Make your pledge here:',
      url: expect.stringContaining('/pledge/test-user-id'),
    });
  });

  it('handles update error gracefully', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
        }),
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: null, error: { message: 'Update failed' } }),
          }),
        }),
      }),
    });
    
    renderCreatePledge();
    
    await waitFor(() => {
      const createButton = screen.getByText('Create Pledge Card');
      fireEvent.click(createButton);
    });
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Error',
        description: 'Failed to create pledge card. Please try again.',
        variant: 'destructive',
      });
    });
  });
});
