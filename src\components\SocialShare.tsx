import React from 'react';
import { Button } from '@/components/ui/button';
import { Share2, Facebook, Twitter, MessageCircle, Mail, Copy } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Type for window with gtag
declare global {
  interface Window {
    gtag?: (command: string, action: string, parameters: Record<string, unknown>) => void;
  }
}

interface SocialShareProps {
  url?: string;
  title?: string;
  description?: string;
  hashtags?: string[];
  className?: string;
  showLabel?: boolean;
}

const SocialShare: React.FC<SocialShareProps> = ({
  url = typeof window !== 'undefined' ? window.location.href : 'https://p4love.com',
  title = "PledgeForLove Uganda | Digital Wedding Contribution Platform",
  description = "Transform traditional wedding contributions with beautiful digital pledge cards for Ugandan weddings.",
  hashtags = ["UgandaWedding", "PledgeForLove", "DigitalWedding"],
  className = "",
  showLabel = true
}) => {
  const { toast } = useToast();

  const shareData = {
    title,
    text: description,
    url
  };

  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);
  const encodedHashtags = hashtags.map(tag => encodeURIComponent(tag)).join(',');

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedTitle}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}&hashtags=${encodedHashtags}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
    email: `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${encodedUrl}`,
  };

  const handleNativeShare = async () => {
    if (typeof navigator !== 'undefined' && navigator.share) {
      try {
        await navigator.share(shareData);
        toast({
          title: "Shared successfully!",
          description: "Thank you for sharing PledgeForLove Uganda.",
        });
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error);
          handleCopyLink();
        }
      }
    } else {
      handleCopyLink();
    }
  };

  const handleCopyLink = async () => {
    try {
      if (typeof navigator !== 'undefined' && navigator.clipboard) {
        await navigator.clipboard.writeText(url);
        toast({
          title: "Link copied!",
          description: "The link has been copied to your clipboard.",
        });
      } else {
        // Fallback for older browsers
        try {
          const textArea = document.createElement('textarea');
          textArea.value = url;
          textArea.style.position = 'fixed';
          textArea.style.opacity = '0';
          document.body.appendChild(textArea);
          textArea.select();
          const successful = document.execCommand('copy');
          document.body.removeChild(textArea);

          if (successful) {
            toast({
              title: "Link copied!",
              description: "The link has been copied to your clipboard.",
            });
          } else {
            throw new Error('Copy command failed');
          }
        } catch (fallbackError) {
          toast({
            title: "Copy failed",
            description: "Please copy the link manually: " + url,
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast({
        title: "Copy failed",
        description: "Please copy the link manually: " + url,
        variant: "destructive",
      });
    }
  };

  const handleSocialShare = (platform: keyof typeof shareLinks) => {
    window.open(shareLinks[platform], '_blank', 'width=600,height=400');

    // Track sharing event (you can integrate with analytics here)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'share', {
        method: platform,
        content_type: 'website',
        item_id: url
      });
    }
  };

  return (
    <div className={`flex flex-wrap gap-2 items-center ${className}`}>
      {showLabel && (
        <span className="text-sm text-gray-600 mr-2">Share:</span>
      )}
      
      {/* Native Share (Mobile) */}
      {typeof navigator !== 'undefined' && navigator.share && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleNativeShare}
          aria-label="Share via device sharing options"
          className="flex items-center gap-2"
        >
          <Share2 className="h-4 w-4" />
          <span className="hidden sm:inline">Share</span>
        </Button>
      )}

      {/* Facebook */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleSocialShare('facebook')}
        aria-label="Share on Facebook"
        className="flex items-center gap-2 text-blue-600 hover:bg-blue-50"
      >
        <Facebook className="h-4 w-4" />
        <span className="hidden sm:inline">Facebook</span>
      </Button>

      {/* Twitter */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleSocialShare('twitter')}
        aria-label="Share on Twitter"
        className="flex items-center gap-2 text-blue-400 hover:bg-blue-50"
      >
        <Twitter className="h-4 w-4" />
        <span className="hidden sm:inline">Twitter</span>
      </Button>

      {/* WhatsApp */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleSocialShare('whatsapp')}
        aria-label="Share on WhatsApp"
        className="flex items-center gap-2 text-green-600 hover:bg-green-50"
      >
        <MessageCircle className="h-4 w-4" />
        <span className="hidden sm:inline">WhatsApp</span>
      </Button>

      {/* Email */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleSocialShare('email')}
        aria-label="Share via Email"
        className="flex items-center gap-2 text-gray-600 hover:bg-gray-50"
      >
        <Mail className="h-4 w-4" />
        <span className="hidden sm:inline">Email</span>
      </Button>

      {/* Copy Link */}
      <Button
        variant="outline"
        size="sm"
        onClick={handleCopyLink}
        aria-label="Copy link to clipboard"
        className="flex items-center gap-2 text-gray-600 hover:bg-gray-50"
      >
        <Copy className="h-4 w-4" />
        <span className="hidden sm:inline">Copy</span>
      </Button>
    </div>
  );
};

export default SocialShare;

// Compact version for smaller spaces
export const SocialShareCompact: React.FC<SocialShareProps> = (props) => {
  return (
    <SocialShare
      {...props}
      showLabel={false}
      className={`${props.className} justify-center`}
    />
  );
};

// Floating share button for pages
export const FloatingSocialShare: React.FC<SocialShareProps> = (props) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="fixed bottom-20 right-4 z-30">
      {isOpen && (
        <div className="mb-2 p-3 bg-white rounded-lg shadow-lg border">
          <SocialShare {...props} showLabel={false} className="flex-col gap-1" />
        </div>
      )}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className="rounded-full w-12 h-12 shadow-lg"
        aria-label="Toggle social sharing options"
      >
        <Share2 className="h-5 w-5" />
      </Button>
    </div>
  );
};
