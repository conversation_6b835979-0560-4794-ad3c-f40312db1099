import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { AlertCircle, CheckCircle, Database, User } from 'lucide-react';
import { Tables } from '@/integrations/supabase/types';

interface DiagnosticData {
  user: { id: string; email: string } | null;
  profile: Tables<'profiles'> | null;
  columnExists: boolean;
  adminStatus: boolean;
  error: string | null;
}

const AdminDiagnostic = () => {
  const { user, profile } = useAuth();
  const [diagnosticData, setDiagnosticData] = useState<DiagnosticData | null>(null);
  const [loading, setLoading] = useState(false);

  const runDiagnostic = useCallback(async () => {
    setLoading(true);
    try {
      const results: DiagnosticData = {
        user: user ? { id: user.id, email: user.email } : null,
        profile: null,
        columnExists: false,
        adminStatus: false,
        error: null
      };

      if (user) {
        // Check if profile exists and get all data
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError) {
          results.error = `Profile Error: ${profileError.message}`;
        } else {
          results.profile = profileData;
          results.adminStatus = profileData?.is_admin || false;
        }

        // Check if is_admin column exists
        try {
          const { data: columnCheck, error: columnError } = await supabase
            .from('profiles')
            .select('is_admin')
            .limit(1);

          if (!columnError) {
            results.columnExists = true;
          } else {
            results.columnExists = false;
            results.error = `Column Error: ${columnError.message}`;
          }
        } catch (err) {
          results.columnExists = false;
          results.error = `Column Check Error: ${err}`;
        }
      }

      setDiagnosticData(results);
    } catch (error) {
      console.error('Diagnostic error:', error);
      setDiagnosticData({
        error: `Diagnostic Error: ${error}`,
        user: user ? { id: user.id, email: user.email } : null,
        profile: null,
        columnExists: false,
        adminStatus: false
      });
    } finally {
      setLoading(false);
    }
  }, [user]);

  const fixAdminStatus = async () => {
    if (!user) return;

    try {
      // First, try to add the column if it doesn't exist
      if (!diagnosticData?.columnExists) {
        const { error: alterError } = await supabase.rpc('exec_sql', {
          sql: 'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;'
        });

        if (alterError) {
          console.error('Error adding column:', alterError);
        }
      }

      // Then update the user to admin
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ is_admin: true, updated_at: new Date().toISOString() })
        .eq('id', user.id);

      if (updateError) {
        console.error('Error updating admin status:', updateError);
        alert(`Error: ${updateError.message}`);
      } else {
        alert('Admin status updated! Please refresh the page.');
        runDiagnostic();
      }
    } catch (error) {
      console.error('Fix error:', error);
      alert(`Fix Error: ${error}`);
    }
  };

  useEffect(() => {
    if (user) {
      runDiagnostic();
    }
  }, [user, runDiagnostic]);

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-6 w-6 text-orange-600" />
            Admin Diagnostic Tool
          </CardTitle>
          <CardDescription>
            Diagnose and fix admin access issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex gap-4">
            <Button onClick={runDiagnostic} disabled={loading}>
              {loading ? 'Running...' : 'Run Diagnostic'}
            </Button>
            {diagnosticData && !diagnosticData.adminStatus && (
              <Button onClick={fixAdminStatus} variant="outline">
                Fix Admin Status
              </Button>
            )}
          </div>

          {diagnosticData && (
            <div className="space-y-4">
              {/* User Info */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold flex items-center gap-2 mb-2">
                  <User className="h-4 w-4" />
                  User Information
                </h3>
                <pre className="text-sm bg-gray-100 p-2 rounded">
                  {JSON.stringify(diagnosticData.user, null, 2)}
                </pre>
              </div>

              {/* Profile Info */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold flex items-center gap-2 mb-2">
                  <Database className="h-4 w-4" />
                  Profile Information
                </h3>
                <pre className="text-sm bg-gray-100 p-2 rounded">
                  {JSON.stringify(diagnosticData.profile, null, 2)}
                </pre>
              </div>

              {/* Status Checks */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {diagnosticData.columnExists ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="font-medium">Column Exists</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    {diagnosticData.columnExists ? 'is_admin column exists' : 'is_admin column missing'}
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {diagnosticData.adminStatus ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="font-medium">Admin Status</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    {diagnosticData.adminStatus ? 'User is admin' : 'User is not admin'}
                  </p>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {diagnosticData.profile ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="font-medium">Profile Exists</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    {diagnosticData.profile ? 'Profile found' : 'Profile missing'}
                  </p>
                </div>
              </div>

              {/* Error Info */}
              {diagnosticData.error && (
                <div className="border border-red-200 rounded-lg p-4 bg-red-50">
                  <h3 className="font-semibold text-red-800 mb-2">Error Details</h3>
                  <p className="text-sm text-red-700">{diagnosticData.error}</p>
                </div>
              )}

              {/* SQL Commands */}
              <div className="border rounded-lg p-4 bg-blue-50">
                <h3 className="font-semibold text-blue-800 mb-2">Manual Fix (Run in Supabase SQL Editor)</h3>
                <div className="space-y-2">
                  <div className="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm">
                    <div>-- Add column if missing</div>
                    <div>ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;</div>
                    <div></div>
                    <div>-- Make user admin</div>
                    <div>UPDATE profiles SET is_admin = TRUE WHERE email = '<EMAIL>';</div>
                    <div></div>
                    <div>-- Verify</div>
                    <div>SELECT id, email, treasurer_name, is_admin FROM profiles WHERE email = '<EMAIL>';</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminDiagnostic;
