import { describe, it, expect, vi, beforeEach } from 'vitest';
import { exportPledgesToXLSX, exportPledgesToCSV, exportPledgesWithOptions } from '../exportUtils';
import type { Pledge } from '@/types/app';

// Mock XLSX
vi.mock('xlsx', () => ({
  utils: {
    book_new: vi.fn(() => ({})),
    json_to_sheet: vi.fn(() => ({ '!cols': [] })),
    book_append_sheet: vi.fn(),
  },
  writeFile: vi.fn(),
}));

// Mock DOM methods
Object.defineProperty(global, 'document', {
  value: {
    createElement: vi.fn(() => ({
      setAttribute: vi.fn(),
      style: { visibility: '' },
      click: vi.fn(),
    })),
    body: {
      appendChild: vi.fn(),
      removeChild: vi.fn(),
    },
  },
});

Object.defineProperty(global, 'URL', {
  value: {
    createObjectURL: vi.fn(() => 'mock-url'),
  },
});

Object.defineProperty(global, 'Blob', {
  value: class MockBlob {
    constructor(public content: unknown[], public options: Record<string, unknown>) {}
  },
});

describe('exportUtils', () => {
  const mockPledges: Pledge[] = [
    {
      id: '1',
      user_id: 'user1',
      guest_name: 'John Doe',
      guest_phone: '+256700123456',
      amount_pledged: 100000,
      amount_paid: 50000,
      payment_status: 'partial',
      pledge_date: '2024-01-15T10:00:00Z',
      payment_date: '2024-01-20T10:00:00Z',
      notes: 'Test pledge',
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-20T10:00:00Z',
    },
    {
      id: '2',
      user_id: 'user1',
      guest_name: 'Jane Smith',
      guest_phone: null,
      amount_pledged: 200000,
      amount_paid: 200000,
      payment_status: 'completed',
      pledge_date: '2024-01-16T10:00:00Z',
      payment_date: '2024-01-25T10:00:00Z',
      notes: null,
      created_at: '2024-01-16T10:00:00Z',
      updated_at: '2024-01-25T10:00:00Z',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('exportPledgesToXLSX', () => {
    it('should export pledges to XLSX format successfully', () => {
      const result = exportPledgesToXLSX(mockPledges);

      expect(result.success).toBe(true);
      expect(result.recordCount).toBe(2);
      expect(result.filename).toMatch(/pledges_export_\d{4}-\d{2}-\d{2}\.xlsx/);
    });

    it('should handle custom filename', () => {
      const customFilename = 'custom_export.xlsx';
      const result = exportPledgesToXLSX(mockPledges, customFilename);

      expect(result.success).toBe(true);
      expect(result.filename).toBe(customFilename);
    });

    it('should handle empty pledges array', () => {
      const result = exportPledgesToXLSX([]);

      expect(result.success).toBe(true);
      expect(result.recordCount).toBe(0);
    });
  });

  describe('exportPledgesToCSV', () => {
    it('should export pledges to CSV format successfully', () => {
      const result = exportPledgesToCSV(mockPledges);

      expect(result.success).toBe(true);
      expect(result.recordCount).toBe(2);
      expect(result.filename).toMatch(/pledges_export_\d{4}-\d{2}-\d{2}\.csv/);
    });

    it('should handle custom filename', () => {
      const customFilename = 'custom_export.csv';
      const result = exportPledgesToCSV(mockPledges, customFilename);

      expect(result.success).toBe(true);
      expect(result.filename).toBe(customFilename);
    });
  });

  describe('exportPledgesWithOptions', () => {
    it('should filter pledges by status', () => {
      const options = {
        statusFilter: ['completed'],
      };

      const result = exportPledgesWithOptions(mockPledges, options);

      expect(result.success).toBe(true);
      // Should only export completed pledges (1 out of 2)
      expect(result.recordCount).toBe(1);
    });

    it('should filter pledges by date range', () => {
      const options = {
        dateRange: {
          start: new Date('2024-01-16'),
          end: new Date('2024-01-17'),
        },
      };

      const result = exportPledgesWithOptions(mockPledges, options);

      expect(result.success).toBe(true);
      // Should only export pledges within date range (1 out of 2)
      expect(result.recordCount).toBe(1);
    });

    it('should apply multiple filters', () => {
      const options = {
        statusFilter: ['partial', 'completed'],
        dateRange: {
          start: new Date('2024-01-15'),
          end: new Date('2024-01-20'),
        },
      };

      const result = exportPledgesWithOptions(mockPledges, options);

      expect(result.success).toBe(true);
      // Should export pledges matching both filters
      expect(result.recordCount).toBe(2);
    });
  });
});
