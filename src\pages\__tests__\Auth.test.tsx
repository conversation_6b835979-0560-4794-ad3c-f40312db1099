import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi } from 'vitest';
import Auth from '../Auth';

interface MockFormProps {
  onSubmit: (data: any) => void;
  loading: boolean;
}

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      signInWithPassword: vi.fn(),
      signUp: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: { subscription: { unsubscribe: vi.fn() } },
      })),
    },
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
    })),
  },
}));

// Mock toast
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: vi.fn() }),
}));

// Mock auth forms
vi.mock('@/components/auth/SignInForm', () => ({
  SignInForm: ({ onSubmit, loading }: MockFormProps) => (
    <form
      data-testid="signin-form"
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({ email: '<EMAIL>', password: 'password123' });
      }}
    >
      <button type="submit" disabled={loading}>
        {loading ? 'Signing In...' : 'Sign In'}
      </button>
    </form>
  ),
}));

vi.mock('@/components/auth/SignUpForm', () => ({
  SignUpForm: ({ onSubmit, loading }: MockFormProps) => (
    <form
      data-testid="signup-form"
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          email: '<EMAIL>',
          password: 'password123',
          brideName: 'Jane',
          groomName: 'John',
          treasurerName: 'Treasurer',
          treasurerPhone: '+************',
          weddingDate: '2024-12-25',
          venue: 'Test Venue',
        });
      }}
    >
      <button type="submit" disabled={loading}>
        {loading ? 'Creating Account...' : 'Create Account'}
      </button>
    </form>
  ),
}));

// Mock validation
vi.mock('@/components/auth/validation', () => ({
  validateSignUpData: vi.fn(),
}));

const renderAuth = () => {
  return render(
    <BrowserRouter>
      <Auth />
    </BrowserRouter>
  );
};

describe('Auth Page', () => {
  let supabase: { auth: { signUp: any; signInWithPassword: any } };
  let toast: { toast: any };

  beforeEach(async () => {
    vi.clearAllMocks();
    const supabaseModule = await import('@/integrations/supabase/client');
    const toastModule = await import('@/hooks/use-toast');
    supabase = supabaseModule.supabase;
    toast = (toastModule.useToast as any)().toast;
  });

  it('renders sign in form by default', () => {
    renderAuth();
    
    expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    expect(screen.getByText('Sign in to manage your wedding pledges')).toBeInTheDocument();
    expect(screen.getByTestId('signin-form')).toBeInTheDocument();
  });

  it('switches to sign up form when toggle is clicked', () => {
    renderAuth();
    
    const toggleButton = screen.getByText(/Don't have an account/);
    fireEvent.click(toggleButton);
    
    expect(screen.getByText('Create Your Wedding Account')).toBeInTheDocument();
    expect(screen.getByText('Set up your wedding pledge management account')).toBeInTheDocument();
    expect(screen.getByTestId('signup-form')).toBeInTheDocument();
  });

  it('switches back to sign in form from sign up', () => {
    renderAuth();
    
    // Switch to sign up
    const signUpToggle = screen.getByText(/Don't have an account/);
    fireEvent.click(signUpToggle);
    
    // Switch back to sign in
    const signInToggle = screen.getByText(/Already have an account/);
    fireEvent.click(signInToggle);
    
    expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    expect(screen.getByTestId('signin-form')).toBeInTheDocument();
  });

  it('handles successful sign in', async () => {
    supabase.auth.signInWithPassword.mockResolvedValue({ error: null });

    renderAuth();

    const form = screen.getByTestId('signin-form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    expect(toast).toHaveBeenCalledWith({
      title: 'Success!',
      description: 'You have been signed in successfully.',
    });

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  it('handles sign in error', async () => {
    const error = { message: 'Invalid credentials' };
    supabase.auth.signInWithPassword.mockResolvedValue({ error });

    renderAuth();

    const form = screen.getByTestId('signin-form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(toast).toHaveBeenCalledWith({
        title: 'Authentication Error',
        description: 'Invalid credentials',
        variant: 'destructive',
      });
    });

    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('handles successful sign up', async () => {
    supabase.auth.signUp.mockResolvedValue({
      error: null,
      data: { user: { id: 'test-user-id' } },
    });

    supabase.from.mockReturnValue({
      insert: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ error: null }),
        }),
      }),
    });

    renderAuth();

    // Switch to sign up
    const toggleButton = screen.getByText(/Don't have an account/);
    fireEvent.click(toggleButton);

    const form = screen.getByTestId('signup-form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(supabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    expect(toast).toHaveBeenCalledWith({
      title: 'Success!',
      description: 'Account created successfully! You can now sign in.',
    });

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  it('shows loading state during authentication', async () => {
    supabase.auth.signInWithPassword.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({ error: null }), 100))
    );

    renderAuth();

    const form = screen.getByTestId('signin-form');
    fireEvent.submit(form);

    expect(screen.getByText('Signing In...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Sign In')).toBeInTheDocument();
    });
  });

  it('renders heart icon', () => {
    renderAuth();
    
    const heartIcon = screen.getByRole('img', { hidden: true });
    expect(heartIcon).toBeInTheDocument();
  });

  it('has proper semantic structure', () => {
    renderAuth();

    // Check for proper heading
    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toHaveTextContent('Welcome Back');

    // Check for card structure
    expect(screen.getByRole('form')).toBeInTheDocument();
  });

  it('handles signup with optional wedding date and venue', async () => {
    // Mock SignUpForm to submit without wedding date and venue
    const mockSignUpFormWithoutOptionals = vi.fn().mockImplementation(({ onSubmit, loading }: MockFormProps) => (
      <form
        data-testid="signup-form-minimal"
        onSubmit={(e) => {
          e.preventDefault();
          onSubmit({
            email: '<EMAIL>',
            password: 'password123',
            brideName: 'Jane',
            groomName: 'John',
            treasurerName: 'Treasurer',
            treasurerPhone: '',
            weddingDate: '',
            venue: '',
          });
        }}
      >
        <button type="submit" disabled={loading}>
          {loading ? 'Creating Account...' : 'Create Account'}
        </button>
      </form>
    ));

    // Temporarily replace the mock
    vi.doMock('@/components/auth/SignUpForm', () => ({
      SignUpForm: mockSignUpFormWithoutOptionals,
    }));

    supabase.auth.signUp.mockResolvedValue({
      error: null,
      data: { user: { id: 'test-user-id' } },
    });

    supabase.from.mockReturnValue({
      insert: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ error: null }),
        }),
      }),
    });

    renderAuth();

    // Switch to sign up
    const toggleButton = screen.getByText(/Don't have an account/);
    fireEvent.click(toggleButton);

    const form = screen.getByTestId('signup-form-minimal');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(supabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    // Verify that profile creation handles empty optional fields
    await waitFor(() => {
      expect(supabase.from).toHaveBeenCalledWith('profiles');
    });
  });
});
