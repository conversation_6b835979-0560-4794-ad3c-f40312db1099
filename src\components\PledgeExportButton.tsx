import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, FileSpreadsheet, FileText, Settings } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { Pledge, PaymentStatus } from "@/types/app";
import { exportPledgesToXLSX, exportPledgesToCSV, exportPledgesWithOptions, type ExportOptions } from "@/utils/exportUtils";

interface PledgeExportButtonProps {
  pledges: Pledge[];
  disabled?: boolean;
}

const PledgeExportButton = ({ pledges, disabled = false }: PledgeExportButtonProps) => {
  const [showAdvancedDialog, setShowAdvancedDialog] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    filename: '',
    includeMetadata: true,
    statusFilter: [],
  });
  const [isExporting, setIsExporting] = useState(false);
  
  const { toast } = useToast();

  const handleQuickExport = async (format: 'xlsx' | 'csv') => {
    if (pledges.length === 0) {
      toast({
        title: "No Data to Export",
        description: "There are no pledges to export.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    
    try {
      let result;
      if (format === 'xlsx') {
        result = exportPledgesToXLSX(pledges);
      } else {
        result = exportPledgesToCSV(pledges);
      }

      if (result.success) {
        toast({
          title: "Export Successful! 📊",
          description: `Exported ${result.recordCount} pledges to ${result.filename}`,
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export pledges",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleAdvancedExport = async () => {
    if (pledges.length === 0) {
      toast({
        title: "No Data to Export",
        description: "There are no pledges to export.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    
    try {
      const result = exportPledgesWithOptions(pledges, exportOptions);

      if (result.success) {
        toast({
          title: "Export Successful! 📊",
          description: `Exported ${result.recordCount} pledges to ${result.filename}`,
        });
        setShowAdvancedDialog(false);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export pledges",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const updateExportOptions = <K extends keyof ExportOptions>(key: K, value: ExportOptions[K]) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  const toggleStatusFilter = (status: PaymentStatus) => {
    const currentFilters = exportOptions.statusFilter || [];
    const newFilters = currentFilters.includes(status)
      ? currentFilters.filter(s => s !== status)
      : [...currentFilters, status];
    
    updateExportOptions('statusFilter', newFilters);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            disabled={disabled || isExporting}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>Quick Export</DropdownMenuLabel>
          <DropdownMenuItem onClick={() => handleQuickExport('xlsx')}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Export to Excel (.xlsx)
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleQuickExport('csv')}>
            <FileText className="h-4 w-4 mr-2" />
            Export to CSV
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setShowAdvancedDialog(true)}>
            <Settings className="h-4 w-4 mr-2" />
            Advanced Export...
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={showAdvancedDialog} onOpenChange={setShowAdvancedDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Advanced Export Options</DialogTitle>
            <DialogDescription>
              Customize your export with filters and options
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Filename */}
            <div className="space-y-2">
              <Label htmlFor="filename">Custom Filename (optional)</Label>
              <Input
                id="filename"
                placeholder="pledges_export_2024"
                value={exportOptions.filename || ''}
                onChange={(e) => updateExportOptions('filename', e.target.value)}
              />
              <p className="text-sm text-gray-500">
                Leave empty for auto-generated filename
              </p>
            </div>

            {/* Include Metadata */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="metadata"
                checked={exportOptions.includeMetadata}
                onCheckedChange={(checked) => updateExportOptions('includeMetadata', checked)}
              />
              <Label htmlFor="metadata">Include summary sheet with statistics</Label>
            </div>

            {/* Status Filter */}
            <div className="space-y-3">
              <Label>Payment Status Filter</Label>
              <div className="space-y-2">
                {(['pending', 'partial', 'completed'] as PaymentStatus[]).map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}`}
                      checked={exportOptions.statusFilter?.includes(status) || false}
                      onCheckedChange={() => toggleStatusFilter(status)}
                    />
                    <Label htmlFor={`status-${status}`} className="capitalize">
                      {status} payments
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-500">
                Leave all unchecked to include all statuses
              </p>
            </div>

            {/* Export Summary */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Export Preview</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p>Total pledges: {pledges.length}</p>
                {exportOptions.statusFilter && exportOptions.statusFilter.length > 0 && (
                  <p>
                    Filtered pledges: {pledges.filter(p => exportOptions.statusFilter!.includes(p.payment_status as PaymentStatus)).length}
                  </p>
                )}
                <p>Format: Excel (.xlsx)</p>
                <p>Include summary: {exportOptions.includeMetadata ? 'Yes' : 'No'}</p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowAdvancedDialog(false)}
                disabled={isExporting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAdvancedExport}
                disabled={isExporting}
              >
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                {isExporting ? 'Exporting...' : 'Export Excel'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PledgeExportButton;
