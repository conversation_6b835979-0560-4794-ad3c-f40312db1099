import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Header from '../Header';
import { supabase } from '@/integrations/supabase/client';

type MockFunction = ReturnType<typeof vi.fn>;

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(),
      signOut: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          maybeSingle: vi.fn(),
        })),
      })),
    })),
  },
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock AuthModal context
const mockOpenAuthModal = vi.fn();
vi.mock('@/contexts/AuthModalContext', () => ({
  useAuthModal: () => ({
    openAuthModal: mockOpenAuthModal,
    closeAuthModal: vi.fn(),
    isAuthModalOpen: false,
    authModalMode: 'signin',
  }),
}));

const renderHeader = () => {
  return render(
    <BrowserRouter>
      <Header />
    </BrowserRouter>
  );
};

describe('Header Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementations
    const mockSubscription = { unsubscribe: vi.fn() };
    (supabase.auth.onAuthStateChange as MockFunction).mockReturnValue({
      data: { subscription: mockSubscription }
    });
    (supabase.auth.getSession as MockFunction).mockResolvedValue({
      data: { session: null }
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders the logo and brand name', () => {
    renderHeader();

    expect(screen.getByText('PFL')).toBeInTheDocument();

    // Check for heart icon (SVG)
    const heartIcon = screen.getByRole('banner').querySelector('svg.lucide-heart');
    expect(heartIcon).toBeInTheDocument();
  });

  it('shows sign in button when user is not authenticated', async () => {
    renderHeader();
    
    await waitFor(() => {
      expect(screen.getByText('Sign In')).toBeInTheDocument();
      expect(screen.getByText('View Demo')).toBeInTheDocument();
    });
  });

  it('shows user menu when user is authenticated', async () => {
    const mockSession = {
      user: { id: 'test-user-id', email: '<EMAIL>' }
    };
    
    (supabase.auth.getSession as MockFunction).mockResolvedValue({
      data: { session: mockSession }
    });

    const mockProfile = {
      treasurer_name: 'John Doe',
      bride_name: 'Jane',
      groom_name: 'John'
    };

    (supabase.from as MockFunction).mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockProfile,
            error: null
          })
        })
      })
    });

    renderHeader();

    await waitFor(() => {
      expect(screen.getByText('Welcome, John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane & John')).toBeInTheDocument();
      expect(screen.getByText('Profile')).toBeInTheDocument();
      expect(screen.getByText('Sign Out')).toBeInTheDocument();
    });
  });

  it('navigates to home when logo is clicked', () => {
    renderHeader();
    
    const logo = screen.getByText('PFL').closest('div');
    fireEvent.click(logo!);
    
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('opens auth modal when sign in is clicked', () => {
    renderHeader();

    const signInButton = screen.getByText('Sign In');
    fireEvent.click(signInButton);

    expect(mockOpenAuthModal).toHaveBeenCalledWith('signin');
  });

  it('navigates to demo when view demo is clicked', () => {
    renderHeader();
    
    const demoButton = screen.getByText('View Demo');
    fireEvent.click(demoButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/demo-pledge');
  });

  it('handles sign out correctly', async () => {
    const mockSession = {
      user: { id: 'test-user-id', email: '<EMAIL>' }
    };
    
    (supabase.auth.getSession as MockFunction).mockResolvedValue({
      data: { session: mockSession }
    });
    (supabase.auth.signOut as MockFunction).mockResolvedValue({ error: null });

    renderHeader();

    await waitFor(() => {
      const signOutButton = screen.getByText('Sign Out');
      fireEvent.click(signOutButton);
    });

    expect(supabase.auth.signOut).toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('handles sign out error gracefully', async () => {
    const mockSession = {
      user: { id: 'test-user-id', email: '<EMAIL>' }
    };
    
    (supabase.auth.getSession as MockFunction).mockResolvedValue({
      data: { session: mockSession }
    });
    (supabase.auth.signOut as MockFunction).mockResolvedValue({
      error: new Error('Sign out failed')
    });

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    renderHeader();

    await waitFor(() => {
      const signOutButton = screen.getByText('Sign Out');
      fireEvent.click(signOutButton);
    });

    expect(consoleSpy).toHaveBeenCalledWith('Sign out error:', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
